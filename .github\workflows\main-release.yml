# Main分支自动打包发布流程
# 当向main分支提交PR时触发多平台多架构的自动打包和发布
name: 自动打包发布

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
    types: [opened, synchronize, reopened]
  workflow_dispatch:
    inputs:
      version:
        description: '发布版本号 (例如: v1.0.0)'
        required: true
        default: 'v1.0.0'
      include_arm64:
        description: '是否包含ARM64架构 (实验性功能)'
        required: false
        default: false
        type: boolean

# 设置GitHub Actions权限
permissions:
  contents: write  # 允许创建release和上传文件
  pages: write     # 允许部署到GitHub Pages
  id-token: write  # 允许OIDC token写入

jobs:
  # 发布前检查
  pre-release-check:
    name: 发布前检查
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04以避免Ubuntu 24.04兼容性问题

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 运行完整测试
      run: |
        echo "🧪 运行发布前测试..."
        python scripts/run_tests.py
        pytest tests/ -v --cov=src

    - name: 检查PR构建配置
      run: |
        echo "📋 检查PR构建信息..."
        echo "PR编号: ${{ github.event.number }}"
        echo "源分支: ${{ github.head_ref }}"
        echo "目标分支: ${{ github.base_ref }}"
        echo "当前提交: ${{ github.sha }}"
        echo "包含ARM64: ${{ github.event.inputs.include_arm64 || 'false' }}"

        # 检查图标文件
        echo "🎨 检查应用图标文件..."
        ls -la src/resources/icons/

        # 验证构建脚本
        echo "🔧 验证构建脚本..."
        python scripts/build.py --test-only

  # 构建发布版本 - 支持多平台多架构
  build-release:
    name: 构建发布版本
    needs: pre-release-check
    strategy:
      fail-fast: false
      matrix:
        include:
          # Windows x86_64 (主要支持)
          - os: windows-latest
            platform: windows
            arch: x86_64
            runner_arch: X64

          # Linux x86_64 (主要支持)
          - os: ubuntu-22.04  # 明确指定Ubuntu 22.04
            platform: linux
            arch: x86_64
            runner_arch: X64

          # Linux ARM64 (新增支持 - 2025年1月16日已发布)
          - os: ubuntu-24.04-arm
            platform: linux
            arch: arm64
            runner_arch: ARM64
            experimental: false

          # Windows ARM64 (使用新的官方ARM64 runner)
          - os: windows-11-arm
            platform: windows
            arch: arm64
            runner_arch: arm64
            experimental: true

    runs-on: ${{ matrix.os }}

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境 (Windows/macOS)
      if: matrix.platform != 'linux'
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        architecture: ${{ matrix.runner_arch == 'arm64' && 'arm64' || matrix.runner_arch }}

    - name: 设置Python环境 (Linux - 系统包管理器)
      if: matrix.platform == 'linux'
      run: |
        echo "🐍 使用系统包管理器安装Python 3.11 (Linux平台)..."

        # 关键修复：禁用command-not-found的APT hook，解决cnf-update-db脚本问题
        echo "🔧 禁用command-not-found的APT hook，解决cnf-update-db脚本问题..."
        sudo mv /etc/apt/apt.conf.d/50command-not-found /etc/apt/apt.conf.d/50command-not-found.disabled 2>/dev/null || echo "⚠️ command-not-found配置文件不存在，跳过"

        # 现在可以正常使用apt-get update
        echo "📦 更新包缓存..."
        sudo apt-get update

        sudo apt-get install -y software-properties-common
        sudo add-apt-repository ppa:deadsnakes/ppa -y
        sudo apt-get update

        # 安装Python 3.11和相关包
        sudo apt-get install -y python3.11 python3.11-venv python3.11-dev python3.11-distutils

        # 安装pip (deadsnakes PPA中没有python3.11-pip，需要手动安装)
        curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11

        # 设置python3.11为默认python3
        sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1

        # 验证安装
        echo "✅ Python安装完成，版本信息："
        python3 --version
        python3 -m pip --version



    - name: 安装系统依赖 (Linux)
      if: matrix.platform == 'linux'
      run: |
        echo "📦 安装系统依赖包..."
        # command-not-found hook已禁用，可以正常使用apt-get update
        sudo apt-get update

        # 根据架构和Ubuntu版本选择合适的OpenGL包
        if [[ "${{ matrix.arch }}" == "arm64" ]]; then
          echo "🔧 安装ARM64专用依赖..."
          sudo apt-get install -y \
            libgl1-mesa-dev \
            libglib2.0-0 \
            libxkbcommon-x11-0 \
            libxcb-icccm4 \
            libxcb-image0 \
            libxcb-keysyms1 \
            libxcb-randr0 \
            libxcb-render-util0 \
            libxcb-xinerama0 \
            libxcb-xfixes0 \
            libegl1 \
            libxkbcommon0
        else
          echo "🔧 安装x86_64依赖..."
          sudo apt-get install -y \
            libgl1-mesa-glx \
            libglib2.0-0 \
            libxkbcommon-x11-0 \
            libxcb-icccm4 \
            libxcb-image0 \
            libxcb-keysyms1 \
            libxcb-randr0 \
            libxcb-render-util0 \
            libxcb-xinerama0 \
            libxcb-xfixes0
        fi

    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller

    - name: 配置构建环境
      run: |
        echo "🔧 配置 ${{ matrix.platform }}-${{ matrix.arch }} 构建环境..."
        echo "平台: ${{ matrix.platform }}"
        echo "架构: ${{ matrix.arch }}"
        echo "运行器架构: ${{ matrix.runner_arch }}"

        # 设置环境变量
        echo "BUILD_PLATFORM=${{ matrix.platform }}" >> $GITHUB_ENV
        echo "BUILD_ARCH=${{ matrix.arch }}" >> $GITHUB_ENV

    - name: 构建可执行文件
      run: |
        echo "🔨 构建 ${{ matrix.platform }}-${{ matrix.arch }} 版本..."

        # 使用新的构建脚本，传递平台和架构参数
        python scripts/build.py --platform ${{ matrix.platform }} --arch ${{ matrix.arch }}
      shell: bash

    - name: 验证构建结果
      run: |
        echo "✅ 验证 ${{ matrix.platform }}-${{ matrix.arch }} 构建结果..."

        # 检查构建产物 - 适配新的构建脚本输出结构（包含架构）
        DIST_DIR="dist/${{ matrix.platform }}-${{ matrix.arch }}"

        if [[ "${{ matrix.platform }}" == "windows" ]]; then
          # Windows: 单文件模式
          EXECUTABLE="$DIST_DIR/EmailDomainManager.exe"
        else
          # Linux: 目录模式
          EXECUTABLE="$DIST_DIR/EmailDomainManager/EmailDomainManager"
        fi

        if [[ -f "$EXECUTABLE" ]]; then
          echo "✅ 可执行文件构建成功: $EXECUTABLE"
          ls -la "$EXECUTABLE"

          # 检查文件大小
          SIZE=$(stat -c%s "$EXECUTABLE" 2>/dev/null || stat -f%z "$EXECUTABLE" 2>/dev/null || echo "0")
          SIZE_MB=$((SIZE / 1024 / 1024))
          echo "📦 文件大小: ${SIZE_MB}MB"

          if [[ $SIZE_MB -lt 5 ]]; then
            echo "⚠️  警告: 文件大小异常小，可能构建不完整"
          fi
        else
          echo "❌ 可执行文件未找到: $EXECUTABLE"
          echo "📁 dist目录内容:"
          find dist/ -type f -ls 2>/dev/null || ls -la dist/ 2>/dev/null || echo "dist目录不存在"
          exit 1
        fi
      shell: bash

    - name: 创建发布包
      run: |
        echo "📦 创建 ${{ matrix.platform }}-${{ matrix.arch }} 发布包..."

        # 创建发布目录
        RELEASE_DIR="release-${{ matrix.platform }}-${{ matrix.arch }}"
        mkdir -p "$RELEASE_DIR"

        # 复制可执行文件和相关文件（适配架构）
        DIST_DIR="dist/${{ matrix.platform }}-${{ matrix.arch }}"

        if [[ "${{ matrix.platform }}" == "windows" ]]; then
          # Windows: 直接使用exe文件，不打包
          ARCHIVE_NAME="EmailDomainManager-${{ matrix.platform }}-${{ matrix.arch }}.exe"
          cp "$DIST_DIR/EmailDomainManager.exe" "$ARCHIVE_NAME"

          echo "✅ Windows可执行文件准备完成: $ARCHIVE_NAME"
          echo "📝 Windows版本说明:"
          echo "  - 双击即可运行，无需安装"
          echo "  - 系统要求: Windows 10+ (${{ matrix.arch }})"
          echo "  - 首次运行可能需要确认安全警告"
        else
          # Linux: 目录模式，复制整个目录
          cp -r "$DIST_DIR/EmailDomainManager"/* "$RELEASE_DIR/"

          # 创建README文件
          echo "域名邮箱管理器 Linux版本" > "$RELEASE_DIR/README.txt"
          echo "" >> "$RELEASE_DIR/README.txt"
          echo "安装说明：" >> "$RELEASE_DIR/README.txt"
          echo "1. 解压文件： tar -xzf EmailDomainManager-linux-${{ matrix.arch }}.tar.gz" >> "$RELEASE_DIR/README.txt"
          echo "2. 进入目录： cd EmailDomainManager-linux-${{ matrix.arch }}" >> "$RELEASE_DIR/README.txt"
          echo "3. 添加执行权限： chmod +x EmailDomainManager" >> "$RELEASE_DIR/README.txt"
          echo "4. 运行程序： ./EmailDomainManager" >> "$RELEASE_DIR/README.txt"
          echo "" >> "$RELEASE_DIR/README.txt"
          echo "系统要求：" >> "$RELEASE_DIR/README.txt"
          echo "- Linux发行版 (Ubuntu 18.04+等) (${{ matrix.arch }})" >> "$RELEASE_DIR/README.txt"
          echo "- 4GB RAM (推荐)" >> "$RELEASE_DIR/README.txt"
          echo "- 100MB 磁盘空间" >> "$RELEASE_DIR/README.txt"
          echo "- X11或Wayland显示服务器" >> "$RELEASE_DIR/README.txt"
          echo "" >> "$RELEASE_DIR/README.txt"
          echo "注意事项：" >> "$RELEASE_DIR/README.txt"
          echo "- 确保系统已安装Qt6运行时库" >> "$RELEASE_DIR/README.txt"
          echo "- 如遇到依赖问题，请安装： sudo apt install qt6-base-dev" >> "$RELEASE_DIR/README.txt"

          ARCHIVE_NAME="EmailDomainManager-${{ matrix.platform }}-${{ matrix.arch }}.tar.gz"

          # Linux平台使用tar.gz打包
          tar -czf "$ARCHIVE_NAME" -C "$RELEASE_DIR" .
        fi

        echo "✅ 发布包创建完成: $ARCHIVE_NAME"
        ls -la "$ARCHIVE_NAME"

        # 设置输出变量
        echo "ARCHIVE_NAME=$ARCHIVE_NAME" >> $GITHUB_ENV
        echo "RELEASE_DIR=$RELEASE_DIR" >> $GITHUB_ENV
      shell: bash

    - name: 上传构建产物
      uses: actions/upload-artifact@v4
      with:
        name: build-${{ matrix.platform }}-${{ matrix.arch }}
        path: |
          ${{ env.ARCHIVE_NAME }}
          ${{ env.RELEASE_DIR }}/
        retention-days: 30

  # 创建GitHub Release (临时允许PR触发，用于测试)
  create-release:
    name: 创建GitHub Release
    needs: build-release
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch' || github.event_name == 'pull_request'

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 下载所有构建产物
      uses: actions/download-artifact@v4
      with:
        path: artifacts/

    - name: 整理构建产物
      run: |
        echo "📁 整理构建产物..."
        find artifacts/ -name "*.exe" -o -name "*.tar.gz" | while read file; do
          echo "发现构建产物: $file"
          cp "$file" ./
        done

        echo "📦 最终发布文件:"
        ls -la *.exe *.tar.gz 2>/dev/null || echo "没有找到发布文件"

    - name: 生成发布说明
      run: |
        echo "📝 生成发布说明..."

        # 获取最新的提交信息作为发布说明
        RELEASE_NOTES=$(git log --pretty=format:"- %s" -10)

        # 根据事件类型设置发布类型标识
        if [[ "${{ github.event_name }}" == "pull_request" ]]; then
          RELEASE_TYPE="🧪 测试版本 (PR #${{ github.event.number }})"
          RELEASE_TITLE="域名邮箱管理器 测试版本"
        else
          RELEASE_TYPE="🎉 正式发布版本"
          RELEASE_TITLE="域名邮箱管理器 多平台发布版本"
        fi

        cat > release-notes.md << EOF
        # $RELEASE_TITLE

        ## $RELEASE_TYPE

        ## 🎉 新功能和改进

        $RELEASE_NOTES

        ## 📦 下载说明

        ### 支持的平台和架构

        - **Windows x86_64**: 下载 \`EmailDomainManager-windows-x86_64.exe\`
        - **Windows ARM64**: 下载 \`EmailDomainManager-windows-arm64.exe\` (实验性支持)
        - **Linux x86_64**: 下载 \`EmailDomainManager-linux-x86_64.tar.gz\`
        - **Linux ARM64**: 下载 \`EmailDomainManager-linux-arm64.tar.gz\`

        ## 🚀 安装说明

        ### Windows用户
        1. **x86_64设备**: 下载 \`EmailDomainManager-windows-x86_64.exe\`
        2. **ARM64设备**: 下载 \`EmailDomainManager-windows-arm64.exe\` (Surface Pro X等ARM设备)
        3. 双击exe文件直接运行，无需安装

        ### Linux用户
        1. **x86_64设备**: 下载 \`EmailDomainManager-linux-x86_64.tar.gz\`
        2. **ARM64设备**: 下载 \`EmailDomainManager-linux-arm64.tar.gz\` (树莓派、ARM服务器等)
        3. 解压: \`tar -xzf EmailDomainManager-linux-架构.tar.gz\`
        4. 添加执行权限: \`chmod +x EmailDomainManager\`
        5. 运行: \`./EmailDomainManager\`

        ## 📋 系统要求

        - **Windows**: Windows 10+ (x86_64/ARM64)
        - **Linux**: 现代Linux发行版 (x86_64/ARM64)，支持X11或Wayland
        - **内存**: 4GB+ RAM (推荐)
        - **存储**: 100MB+ 磁盘空间

        ## 🎨 应用特性

        - 基于PyQt6的现代化界面
        - Material Design设计风格
        - 支持Windows (.ico) 和 Linux (.png) 平台图标
        - 完整的邮箱管理功能

        ## 🐛 问题反馈

        如有问题请在 [Issues](https://github.com/your-username/email-domain-manager/issues) 中反馈。
        EOF

    - name: 确定版本号
      id: version
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          VERSION="${{ github.event.inputs.version }}"
        elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
          # PR测试版本：包含PR编号
          VERSION="v$(date +'%Y.%m.%d')-pr${{ github.event.number }}-test${{ github.run_number }}"
        else
          # 自动生成版本号：v年.月.日-构建号
          VERSION="v$(date +'%Y.%m.%d')-${{ github.run_number }}"
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "发布版本: $VERSION"

    - name: 创建Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.version.outputs.version }}
        name: 域名邮箱管理器 ${{ steps.version.outputs.version }}
        body_path: release-notes.md
        draft: false
        prerelease: ${{ github.event_name == 'pull_request' }}
        files: |
          EmailDomainManager-windows-x86_64.exe
          EmailDomainManager-windows-arm64.exe
          EmailDomainManager-linux-x86_64.tar.gz
          EmailDomainManager-linux-arm64.tar.gz
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # 部署文档 (临时允许PR触发，用于测试)
  deploy-docs:
    name: 部署文档
    needs: create-release
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch' || github.event_name == 'pull_request'

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 安装文档工具
      run: |
        python -m pip install --upgrade pip
        pip install mkdocs mkdocs-material

    - name: 构建文档
      run: |
        echo "📚 构建项目文档..."

        # 创建mkdocs配置
        echo "site_name: 域名邮箱管理器" > mkdocs.yml
        echo "site_description: 基于PyQt6的域名邮箱管理工具 - 多平台自动打包发布" >> mkdocs.yml
        echo "" >> mkdocs.yml
        echo "nav:" >> mkdocs.yml
        echo "  - 首页: README.md" >> mkdocs.yml
        echo "  - 下载安装: docs/installation.md" >> mkdocs.yml
        echo "  - 使用指南: docs/user-guide.md" >> mkdocs.yml
        echo "  - 开发文档: docs/development.md" >> mkdocs.yml
        echo "  - API规范: docs/api-specification.md" >> mkdocs.yml
        echo "  - 更新日志: docs/changelog.md" >> mkdocs.yml
        echo "" >> mkdocs.yml
        echo "theme:" >> mkdocs.yml
        echo "  name: material" >> mkdocs.yml
        echo "  language: zh" >> mkdocs.yml

        # 创建基础文档结构
        mkdir -p docs

        # 创建安装文档
        echo "# 下载与安装" > docs/installation.md
        echo "" >> docs/installation.md
        echo "## 支持的平台" >> docs/installation.md
        echo "" >> docs/installation.md
        echo "- Windows x86_64" >> docs/installation.md
        echo "- Linux x86_64" >> docs/installation.md
        echo "" >> docs/installation.md
        echo "## 下载地址" >> docs/installation.md
        echo "" >> docs/installation.md
        echo "请访问 [GitHub Releases](https://github.com/your-username/email-domain-manager/releases) 下载最新版本。" >> docs/installation.md
        echo "" >> docs/installation.md
        echo "## 安装说明" >> docs/installation.md
        echo "" >> docs/installation.md
        echo "### Windows" >> docs/installation.md
        echo "1. 下载 \`EmailDomainManager-windows-x86_64.zip\`" >> docs/installation.md
        echo "2. 解压到任意目录" >> docs/installation.md
        echo "3. 双击 \`EmailDomainManager.exe\` 运行" >> docs/installation.md
        echo "" >> docs/installation.md
        echo "### Linux" >> docs/installation.md
        echo "1. 下载 \`EmailDomainManager-linux-x86_64.tar.gz\`" >> docs/installation.md
        echo "2. 解压并运行" >> docs/installation.md

        mkdocs build

    - name: 部署到GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./site

  # 构建通知和总结
  build-notification:
    name: 构建通知
    needs: [build-release, create-release, deploy-docs]
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04
    if: always()

    steps:
    - name: 构建结果通知
      run: |
        if [[ "${{ github.event_name }}" == "pull_request" ]]; then
          echo "🔍 PR构建测试和预发布完成"
          echo "========================"
          echo "PR信息:"
          echo "  - PR编号: #${{ github.event.number }}"
          echo "  - 源分支: ${{ github.head_ref }}"
          echo "  - 目标分支: ${{ github.base_ref }}"
          echo "  - 测试版本: ${{ needs.create-release.outputs.version || '未知' }}"
        else
          echo "🎉 正式发布流程完成"
          echo "========================"
        fi

        # 检查构建任务结果
        echo "📋 任务执行结果:"
        echo "  - 构建测试: ${{ needs.build-release.result }}"

        if [[ "${{ github.event_name }}" == "push" ]] || [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          echo "  - 创建Release: ${{ needs.create-release.result }}"
          echo "  - 部署文档: ${{ needs.deploy-docs.result }}"
        fi

        if [[ "${{ contains(needs.*.result, 'failure') }}" == "true" ]]; then
          echo ""
          echo "❌ 流程中出现问题"
          echo "失败的任务:"
          [[ "${{ needs.build-release.result }}" == "failure" ]] && echo "  - 构建测试失败"
          [[ "${{ needs.create-release.result }}" == "failure" ]] && echo "  - 创建Release失败"
          [[ "${{ needs.deploy-docs.result }}" == "failure" ]] && echo "  - 部署文档失败"
          echo ""
          echo "请检查上述失败任务的日志以获取详细错误信息。"
        else
          echo ""
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "✅ PR构建测试和预发布成功！"
            echo "📦 所有4个平台构建成功"
            echo "🎯 已创建测试版本Release，可以下载测试"
            echo "✅ 测试通过后可以安全合并到main分支"
          else
            echo "🎉 正式发布成功完成！"
            echo "📦 新版本已发布到GitHub Releases"
            echo "📚 文档已更新到GitHub Pages"
            echo ""
            echo "🔗 相关链接:"
            echo "  - Releases: https://github.com/${{ github.repository }}/releases"
            echo "  - 文档: https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}"
          fi
        fi

        echo ""
        echo "📊 构建统计:"
        echo "  - 支持平台: Windows x86_64/ARM64, Linux x86_64/ARM64"
        echo "  - 构建时间: $(date)"
        echo "  - PR编号: #${{ github.event.number }}"
        echo "  - 源分支: ${{ github.head_ref }}"
        echo "  - 提交SHA: ${{ github.sha }}"
